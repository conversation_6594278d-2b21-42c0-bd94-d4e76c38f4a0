#!/usr/bin/env python3
"""
IR-Alchemist - Modern Material Design Version
Professional Cabinet IR Generator with Stereo Audio Support
"""

import os
import sys
import tempfile
import soundfile as sf
import numpy as np
from scipy.signal import butter, lfilter, fftconvolve
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QGridLayout, QRadioButton, QButtonGroup, QSizePolicy, QLineEdit, QFileDialog,
    QMessageBox, QPushButton, QLabel, QCheckBox, QSplitter, QGraphicsDropShadowEffect,
    QComboBox, QProgressBar, QStatusBar, QMenuBar, QAction, QToolBar, QScrollArea
)
from PyQt5.QtCore import Qt, QUrl, QObject, QThread, pyqtSignal, pyqtSlot, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QFontDatabase, QFont, QPainter, QColor, QPen, QBrush, QPixmap, QKeySequence
import logging

# Import modern UI framework and stereo audio handler
from modern_ui_framework import *
from stereo_audio_handler import stereo_handler
from native_visualization import NativeDetailCanvas as DetailCanvas

# Using native PyQt5 visualization system
logging.info("Using native PyQt5 visualization system")

# Import the IR reader
from irpkg_reader import IRPKGReader

class IRExporter:
    def __init__(self, package_file=None):
        # Import the IR reader
        from irpkg_reader import IRPKGReader
        
        # Initialize the IR dataset reader with packaged dataset
        self.ir_reader = IRPKGReader(package_file)

    def generate_multiple_irs(self, diversity=1.0, count=10, style="American"):
        """
        Generate multiple IRs by randomly selecting from the dataset without any processing.
        
        Args:
            diversity (float): Ignored - kept for interface compatibility
            count (int): Number of IRs to return (default: 10)
            style (str): Ignored - random selection from entire dataset
            
        Returns:
            list: List of raw, unprocessed IR arrays from the dataset
        """
        # Ensure we have IRs available
        if not self.ir_reader.irs:
            logging.warning("No IRs available in dataset")
            return []
        
        # Calculate how many unique IRs we can provide
        total_irs = len(self.ir_reader.irs)
        unique_count = min(count, total_irs)
        
        # Randomly select unique IRs from the entire dataset (ignore style)
        import random
        selected_indices = random.sample(range(total_irs), unique_count)
        
        # Get the raw IRs without any processing
        raw_irs = [self.ir_reader.irs[i].copy() for i in selected_indices]
        
        # If we need more IRs than unique ones available, fill with additional random selections
        if count > unique_count:
            additional_needed = count - unique_count
            for _ in range(additional_needed):
                # Select random IR (may be duplicate now)
                random_index = random.randint(0, total_irs - 1)
                raw_irs.append(self.ir_reader.irs[random_index].copy())
        
        logging.info(f"Selected {len(raw_irs)} raw IRs from dataset ({unique_count} unique)")
        return raw_irs

class IRGenerationWorker(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, exporter, style, count):
        super().__init__()
        self.exporter = exporter
        self.style = style
        self.count = count

    @pyqtSlot()
    def run(self):
        try:
            irs = self.exporter.generate_multiple_irs(count=self.count, style=self.style)
            self.finished.emit(irs)
        except Exception as e:
            self.error.emit(str(e))

class ModernIRGeneratorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IR-Alchemist - Professional Audio Processing")
        self.setGeometry(100, 100, 1600, 1000)

        # Apply Material Design theme
        MaterialTheme.apply_global_style(QApplication.instance())

        # Initialize data
        self.last_generated_irs = None
        self.selected_style = "American"
        self.selected_ir_index = 0
        self.player = QMediaPlayer()
        self.worker_thread = None
        self.current_temp_file = None

        # Initialize responsive grid layout
        self.responsive_layout = None

        self.player.mediaStatusChanged.connect(self._on_media_status_changed)

        # Create modern UI
        self.create_modern_interface()

        # Initialize the exporter
        self.initialize_exporter()

    def resizeEvent(self, event):
        """Handle window resize for responsive design."""
        super().resizeEvent(event)
        if self.responsive_layout:
            self.responsive_layout.update_layout_for_breakpoint(self.width())

            # Update responsive styles based on current breakpoint
            current_breakpoint = self.responsive_layout.get_current_breakpoint(self.width())
            MaterialTheme.update_responsive_styles(self, current_breakpoint)
    
    def create_modern_interface(self):
        """Create the modern Material Design interface with redesigned layout emphasizing Generated IRs."""
        # Set main window background
        self.setStyleSheet(f"background-color: {MaterialColors.BACKGROUND};")

        # Create responsive container
        self.responsive_container = ResponsiveContainer()

        # Create header card (compact version)
        header_card = self.create_compact_header_card()

        # Create main content area with 3-column layout
        # Column 1: Controls (narrow)
        controls_card = self.create_controls_card()
        controls_card.setMaximumWidth(320)
        controls_card.setMinimumWidth(280)

        # Column 2: Generated IRs section (wide - main focus)
        generated_irs_panel = self.create_generated_irs_panel()

        # Column 3: Visualization (medium)
        visualization_panel = self.create_visualization_panel()

        # Add widgets with responsive configurations emphasizing Generated IRs
        self.responsive_container.add_responsive_widget(header_card, {
            'xxl': {'row': 0, 'col': 0, 'colspan': 3},
            'xl': {'row': 0, 'col': 0, 'colspan': 3},
            'lg': {'row': 0, 'col': 0, 'colspan': 3},
            'md': {'row': 0, 'col': 0, 'colspan': 3},
            'sm': {'row': 0, 'col': 0, 'colspan': 1},
            'xs': {'row': 0, 'col': 0, 'colspan': 1}
        })

        # Controls panel - narrow column
        self.responsive_container.add_responsive_widget(controls_card, {
            'xxl': {'row': 1, 'col': 0, 'colspan': 1},
            'xl': {'row': 1, 'col': 0, 'colspan': 1},
            'lg': {'row': 1, 'col': 0, 'colspan': 1},
            'md': {'row': 1, 'col': 0, 'colspan': 1},
            'sm': {'row': 1, 'col': 0, 'colspan': 1},
            'xs': {'row': 1, 'col': 0, 'colspan': 1}
        })

        # Generated IRs panel - main focus, wide column
        self.responsive_container.add_responsive_widget(generated_irs_panel, {
            'xxl': {'row': 1, 'col': 1, 'colspan': 1},
            'xl': {'row': 1, 'col': 1, 'colspan': 1},
            'lg': {'row': 1, 'col': 1, 'colspan': 1},
            'md': {'row': 1, 'col': 1, 'colspan': 1},
            'sm': {'row': 2, 'col': 0, 'colspan': 1},
            'xs': {'row': 2, 'col': 0, 'colspan': 1}
        })

        # Visualization panel - medium column
        self.responsive_container.add_responsive_widget(visualization_panel, {
            'xxl': {'row': 1, 'col': 2, 'colspan': 1},
            'xl': {'row': 1, 'col': 2, 'colspan': 1},
            'lg': {'row': 1, 'col': 2, 'colspan': 1},
            'md': {'row': 1, 'col': 2, 'colspan': 1},
            'sm': {'row': 3, 'col': 0, 'colspan': 1},
            'xs': {'row': 3, 'col': 0, 'colspan': 1}
        })

        # Set column stretch factors to emphasize Generated IRs section
        layout = self.responsive_container.responsive_layout
        layout.setColumnStretch(0, 0)  # Fixed narrow width for controls
        layout.setColumnStretch(1, 2)  # Main focus - Generated IRs gets most space
        layout.setColumnStretch(2, 1)  # Medium width for visualization
        layout.setRowStretch(0, 0)     # Fixed height for header
        layout.setRowStretch(1, 1)     # Expandable for main content

        # Create status bar
        self.create_status_bar()

        self.setCentralWidget(self.responsive_container)

        # Store reference to layout for resize handling
        self.responsive_layout = self.responsive_container.responsive_layout

    def create_compact_header_card(self):
        """Create a compact header card to save space for main content."""
        header_card = MaterialCard(elevation=1)
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(24, 16, 24, 16)
        header_layout.setSpacing(24)
        header_layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        # Title section - horizontal layout for compact design
        title_label = QLabel("IR-Alchemist")
        title_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            font-family: 'Segoe UI', 'Roboto', sans-serif;
            border: none;
            margin: 0;
            padding: 0;
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        # Subtitle - more compact
        subtitle_label = QLabel("Professional Cabinet IR Generator")
        subtitle_label.setStyleSheet(f"""
            font-size: 14px;
            color: {MaterialColors.PRIMARY};
            font-weight: 500;
            border: none;
            margin: 0;
            padding: 0;
        """)
        subtitle_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        subtitle_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_layout.addStretch()

        header_card.setLayout(header_layout)
        return header_card

    def create_header_card(self):
        """Create the modern header card with improved alignment."""
        header_card = MaterialCard(elevation=2)
        header_layout = QVBoxLayout()
        header_layout.setContentsMargins(32, 28, 32, 28)
        header_layout.setSpacing(16)

        # Title section with proper alignment
        title_layout = QHBoxLayout()
        title_layout.setSpacing(16)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        title_label = QLabel("IR-Alchemist")
        title_label.setStyleSheet(f"""
            font-size: 32px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            font-family: 'Segoe UI', 'Roboto', sans-serif;
            border: none;
            margin: 0;
            padding: 0;
            line-height: 1.2;
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        company_label = QLabel("by Develop Device")
        company_label.setStyleSheet(f"""
            font-size: 16px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            font-style: italic;
            border: none;
            margin: 0;
            padding: 4px 0 0 0;
            line-height: 1.2;
        """)
        company_label.setAlignment(Qt.AlignLeft | Qt.AlignBottom)
        company_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        title_layout.addWidget(title_label)
        title_layout.addWidget(company_label)
        title_layout.addStretch()
        header_layout.addLayout(title_layout)

        # Subtitle with clean styling and proper spacing
        subtitle_label = QLabel("Professional Cabinet IR Generator with Stereo Audio Support")
        subtitle_label.setStyleSheet(f"""
            font-size: 18px;
            color: {MaterialColors.PRIMARY};
            font-weight: 500;
            border: none;
            margin: 8px 0 0 0;
            padding: 0;
            line-height: 1.3;
        """)
        subtitle_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        subtitle_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        header_layout.addWidget(subtitle_label)

        # Description with clean styling and proper spacing
        description = QLabel(
            "Generate high-quality impulse responses from our curated dataset. "
            "Select characteristics, generate IRs, and preview with stereo audio processing."
        )
        description.setStyleSheet(f"""
            font-size: 14px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            line-height: 1.5;
            border: none;
            margin: 4px 0 0 0;
            padding: 0;
        """)
        description.setWordWrap(True)
        description.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        description.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        header_layout.addWidget(description)

        header_card.setLayout(header_layout)
        return header_card

    def create_controls_card(self):
        """Create a compact controls card for the narrow left column."""
        controls_card = MaterialCard(elevation=1)
        controls_layout = QVBoxLayout()
        controls_layout.setContentsMargins(20, 24, 20, 24)
        controls_layout.setSpacing(20)
        controls_layout.setAlignment(Qt.AlignTop)

        # Compact card title
        title_label = QLabel("Controls")
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {MaterialColors.PRIMARY};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
            line-height: 1.2;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        controls_layout.addWidget(title_label)

        # Compact style selection section
        style_section = self.create_compact_style_selection_section()
        controls_layout.addWidget(style_section)

        # Compact generation controls
        generation_section = self.create_compact_generation_section()
        controls_layout.addWidget(generation_section)

        # Add stretch to push content to top
        controls_layout.addStretch(1)

        controls_card.setLayout(controls_layout)
        controls_card.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)
        return controls_card

    def create_compact_style_selection_section(self):
        """Create a compact style selection section for the narrow column."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_VARIANT};
            border: none;
        """)

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(16, 14, 16, 14)
        section_layout.setSpacing(10)
        section_layout.setAlignment(Qt.AlignTop)

        # Compact section title
        section_title = QLabel("Sound Style")
        section_title.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 6px 0;
            line-height: 1.2;
        """)
        section_title.setAlignment(Qt.AlignCenter)
        section_title.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(section_title)

        # Compact radio buttons
        self.style_group = QButtonGroup()
        styles = ["American", "British", "German", "Random"]

        for i, style in enumerate(styles):
            radio = MaterialRadioButton(style)
            radio.setStyleSheet(f"""
                QRadioButton {{
                    font-size: 12px;
                    color: {MaterialColors.ON_SURFACE};
                    spacing: 8px;
                    padding: 4px 6px;
                    border: none;
                    margin: 0;
                    border-radius: 4px;
                }}
                QRadioButton:hover {{
                    background-color: {MaterialColors.HOVER};
                }}
                QRadioButton::indicator {{
                    width: 16px;
                    height: 16px;
                    border-radius: 8px;
                    border: 2px solid {MaterialColors.ON_SURFACE_VARIANT};
                    background-color: transparent;
                }}
                QRadioButton::indicator:hover {{
                    border: 2px solid {MaterialColors.PRIMARY};
                    background-color: {MaterialColors.HOVER};
                }}
                QRadioButton::indicator:checked {{
                    background-color: {MaterialColors.PRIMARY};
                    border: 2px solid {MaterialColors.PRIMARY};
                }}
            """)
            radio.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

            if style == "American":
                radio.setChecked(True)
                self.selected_style = style

            radio.toggled.connect(lambda checked, s=style: self.on_style_changed(s, checked))
            self.style_group.addButton(radio)
            section_layout.addWidget(radio)

        section_card.setLayout(section_layout)
        section_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        return section_card

    def create_compact_generation_section(self):
        """Create a compact generation controls section for the narrow column."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_VARIANT};
            border: none;
        """)

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(16, 14, 16, 14)
        section_layout.setSpacing(12)
        section_layout.setAlignment(Qt.AlignTop)

        # Compact section title
        section_title = QLabel("Generate")
        section_title.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 6px 0;
            line-height: 1.2;
        """)
        section_title.setAlignment(Qt.AlignCenter)
        section_title.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(section_title)

        # Compact generate button
        self.generate_button = MaterialButton("Generate IRs", "filled")
        self.generate_button.setMinimumHeight(44)
        self.generate_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MaterialColors.PRIMARY};
                color: {MaterialColors.ON_PRIMARY};
                border: none;
                border-radius: 22px;
                padding: 12px 20px;
                font-weight: 600;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.PRIMARY_VARIANT};
            }}
            QPushButton:disabled {{
                background-color: {MaterialColors.DISABLED};
                color: {MaterialColors.ON_SURFACE_VARIANT};
            }}
        """)
        self.generate_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.generate_button.clicked.connect(self.generate_irs)
        section_layout.addWidget(self.generate_button)

        # Compact progress bar
        self.progress_bar = MaterialProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(self.progress_bar)

        # Compact status label
        self.status_label = QLabel("Ready to generate")
        self.status_label.setStyleSheet(f"""
            font-size: 11px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            border: none;
            margin: 0;
            padding: 4px 0 0 0;
            line-height: 1.3;
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.status_label.setWordWrap(True)
        section_layout.addWidget(self.status_label)

        section_card.setLayout(section_layout)
        section_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        return section_card

    def create_style_selection_section(self):
        """Create the style selection section with clean alignment."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_VARIANT};
            border: none;
        """)

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(20, 18, 20, 18)
        section_layout.setSpacing(12)
        section_layout.setAlignment(Qt.AlignTop)

        # Section title with clean styling and proper alignment
        section_title = QLabel("Sound Characteristic")
        section_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
            line-height: 1.2;
        """)
        section_title.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        section_title.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(section_title)

        # Style radio buttons with improved alignment
        self.style_group = QButtonGroup()
        styles = ["American", "British", "German", "Random"]

        # Create radio button container for better alignment
        radio_container = QWidget()
        radio_layout = QVBoxLayout()
        radio_layout.setContentsMargins(0, 0, 0, 0)
        radio_layout.setSpacing(8)
        radio_layout.setAlignment(Qt.AlignTop)

        for i, style in enumerate(styles):
            radio = MaterialRadioButton(style)
            radio.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

            if style == "American":
                radio.setChecked(True)
                self.selected_style = style

            radio.toggled.connect(lambda checked, s=style: self.on_style_changed(s, checked))
            self.style_group.addButton(radio)
            radio_layout.addWidget(radio)

        radio_container.setLayout(radio_layout)
        section_layout.addWidget(radio_container)

        section_card.setLayout(section_layout)
        section_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        return section_card

    def create_generation_section(self):
        """Create the generation controls section with clean alignment."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_VARIANT};
            border: none;
        """)

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(20, 18, 20, 18)
        section_layout.setSpacing(14)
        section_layout.setAlignment(Qt.AlignTop)

        # Section title with clean styling and proper alignment
        section_title = QLabel("Generation")
        section_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
            line-height: 1.2;
        """)
        section_title.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        section_title.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(section_title)

        # Generate button with proper spacing and alignment
        self.generate_button = MaterialButton("Generate IRs", "filled")
        self.generate_button.setMinimumHeight(48)
        self.generate_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.generate_button.clicked.connect(self.generate_irs)
        section_layout.addWidget(self.generate_button)

        # Progress bar with clean styling and proper alignment
        self.progress_bar = MaterialProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(self.progress_bar)

        # Status label with clean styling and proper alignment
        self.status_label = QLabel("Ready to generate IRs")
        self.status_label.setStyleSheet(f"""
            font-size: 12px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            border: none;
            margin: 0;
            padding: 8px 0 0 0;
            line-height: 1.3;
        """)
        self.status_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        self.status_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.status_label.setWordWrap(True)
        section_layout.addWidget(self.status_label)

        section_card.setLayout(section_layout)
        section_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        return section_card

    def create_export_section(self):
        """Create the export controls section with clean alignment."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_VARIANT};
            border: none;
        """)

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(20, 18, 20, 18)
        section_layout.setSpacing(12)
        section_layout.setAlignment(Qt.AlignTop)

        # Section title with clean styling and proper alignment
        section_title = QLabel("Export")
        section_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
            line-height: 1.2;
        """)
        section_title.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        section_title.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(section_title)

        # Export buttons with consistent spacing and alignment
        self.export_individual_button = MaterialButton("Export Individual", "outlined")
        self.export_individual_button.setMinimumHeight(44)
        self.export_individual_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.export_individual_button.clicked.connect(self.export_individual_irs)
        self.export_individual_button.setEnabled(False)
        section_layout.addWidget(self.export_individual_button)

        self.export_combined_button = MaterialButton("Export Combined", "outlined")
        self.export_combined_button.setMinimumHeight(44)
        self.export_combined_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.export_combined_button.clicked.connect(self.export_combined_ir)
        self.export_combined_button.setEnabled(False)
        section_layout.addWidget(self.export_combined_button)

        section_card.setLayout(section_layout)
        section_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        return section_card

    def create_generated_irs_panel(self):
        """Create the main Generated IRs panel as the central focus of the application."""
        main_panel = MaterialCard(elevation=2)
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(32, 32, 32, 32)
        main_layout.setSpacing(24)

        # Prominent title section
        title_section = QWidget()
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(20)

        # Large, prominent title
        title_label = QLabel("Generated IRs")
        title_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 700;
            color: {MaterialColors.PRIMARY};
            border: none;
            margin: 0;
            padding: 0;
            line-height: 1.2;
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        # Status indicator
        self.ir_status_label = QLabel("Ready to generate IRs")
        self.ir_status_label.setStyleSheet(f"""
            font-size: 14px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            border: none;
            margin: 0;
            padding: 8px 16px;
            background-color: {MaterialColors.SURFACE_VARIANT};
            border-radius: 16px;
        """)
        self.ir_status_label.setAlignment(Qt.AlignCenter)

        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.ir_status_label)
        title_section.setLayout(title_layout)
        main_layout.addWidget(title_section)

        # IR list with enhanced visibility
        self.ir_list_widget = QWidget()
        self.ir_list_widget.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE};
            border: 2px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 12px;
            padding: 16px;
        """)
        self.ir_list_layout = QVBoxLayout()
        self.ir_list_layout.setContentsMargins(16, 16, 16, 16)
        self.ir_list_layout.setSpacing(12)
        self.ir_list_widget.setLayout(self.ir_list_layout)

        # Scroll area for IR list
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.ir_list_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(300)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {MaterialColors.PRIMARY};
                border-radius: 6px;
                min-height: 20px;
            }}
        """)
        main_layout.addWidget(scroll_area, 1)

        # Prominent export controls section
        export_section = self.create_prominent_export_section()
        main_layout.addWidget(export_section)

        main_panel.setLayout(main_layout)
        main_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        return main_panel

    def create_prominent_export_section(self):
        """Create a prominent export section with large, visible buttons."""
        export_card = MaterialCard(elevation=1)
        export_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_CONTAINER_HIGH};
            border: 2px solid {MaterialColors.PRIMARY};
        """)

        export_layout = QVBoxLayout()
        export_layout.setContentsMargins(24, 20, 24, 20)
        export_layout.setSpacing(16)

        # Export section title
        export_title = QLabel("Export Options")
        export_title.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 600;
            color: {MaterialColors.PRIMARY};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
        """)
        export_title.setAlignment(Qt.AlignCenter)
        export_layout.addWidget(export_title)

        # Button container for side-by-side layout
        button_container = QWidget()
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(16)

        # Large, prominent export buttons
        self.export_individual_button = MaterialButton("📁 Export Individual IRs", "filled")
        self.export_individual_button.setMinimumHeight(56)
        self.export_individual_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MaterialColors.PRIMARY};
                color: {MaterialColors.ON_PRIMARY};
                border: none;
                border-radius: 28px;
                padding: 16px 32px;
                font-weight: 600;
                font-size: 16px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.PRIMARY_VARIANT};
            }}
            QPushButton:disabled {{
                background-color: {MaterialColors.DISABLED};
                color: {MaterialColors.ON_SURFACE_VARIANT};
            }}
        """)
        self.export_individual_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.export_individual_button.clicked.connect(self.export_individual_irs)
        self.export_individual_button.setEnabled(False)

        self.export_combined_button = MaterialButton("🔗 Export Combined IR", "outlined")
        self.export_combined_button.setMinimumHeight(56)
        self.export_combined_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {MaterialColors.PRIMARY};
                border: 3px solid {MaterialColors.PRIMARY};
                border-radius: 28px;
                padding: 16px 32px;
                font-weight: 600;
                font-size: 16px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.HOVER};
                border-color: {MaterialColors.PRIMARY_VARIANT};
            }}
            QPushButton:disabled {{
                color: {MaterialColors.DISABLED};
                border-color: {MaterialColors.DISABLED};
            }}
        """)
        self.export_combined_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.export_combined_button.clicked.connect(self.export_combined_ir)
        self.export_combined_button.setEnabled(False)

        button_layout.addWidget(self.export_individual_button)
        button_layout.addWidget(self.export_combined_button)
        button_container.setLayout(button_layout)
        export_layout.addWidget(button_container)

        # Preview button
        self.play_button = MaterialButton("▶ Preview Selected IR", "text")
        self.play_button.setMinimumHeight(48)
        self.play_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                color: {MaterialColors.PRIMARY};
                border: none;
                border-radius: 24px;
                padding: 12px 24px;
                font-weight: 500;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.SURFACE_CONTAINER_HIGH};
            }}
            QPushButton:disabled {{
                color: {MaterialColors.DISABLED};
                background-color: {MaterialColors.SURFACE_VARIANT};
            }}
        """)
        self.play_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.play_button.clicked.connect(self.preview_selected_ir)
        self.play_button.setEnabled(False)
        export_layout.addWidget(self.play_button)

        export_card.setLayout(export_layout)
        return export_card

    def create_right_panel(self):
        """Create the right panel with IR list and visualization."""
        right_widget = QWidget()
        right_layout = QVBoxLayout()
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(24)
        right_layout.setAlignment(Qt.AlignTop)

        # IR List section
        ir_list_card = self.create_ir_list_card()
        ir_list_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        right_layout.addWidget(ir_list_card)

        # Visualization section
        viz_card = self.create_visualization_card()
        viz_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        right_layout.addWidget(viz_card, 1)

        right_widget.setLayout(right_layout)
        right_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        return right_widget

    def create_visualization_panel(self):
        """Create the visualization panel as a separate column."""
        viz_card = MaterialCard(elevation=1)
        viz_layout = QVBoxLayout()
        viz_layout.setContentsMargins(24, 24, 24, 24)
        viz_layout.setSpacing(18)

        # Title with clean styling
        title_label = QLabel("IR Analysis & Visualization")
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
        """)
        title_label.setAlignment(Qt.AlignLeft)
        viz_layout.addWidget(title_label)

        # Detail canvas with clean borders
        self.detail_canvas = DetailCanvas()
        self.detail_canvas.setStyleSheet(f"""
            border: 1px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 8px;
            background-color: {MaterialColors.SURFACE};
        """)
        self.detail_canvas.setMinimumHeight(400)
        viz_layout.addWidget(self.detail_canvas)

        viz_card.setLayout(viz_layout)
        viz_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        return viz_card





    def create_status_bar(self):
        """Create the modern status bar with clean styling."""
        status_bar = QStatusBar()
        status_bar.setStyleSheet(f"""
            QStatusBar {{
                background-color: {MaterialColors.SURFACE_CONTAINER};
                color: {MaterialColors.ON_SURFACE_VARIANT};
                border-top: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-left: none;
                border-right: none;
                border-bottom: none;
                font-size: 12px;
                padding: 8px 16px;
                margin: 0;
            }}
            QStatusBar::item {{
                border: none;
                margin: 0;
                padding: 0;
            }}
        """)
        status_bar.showMessage("Ready - Select characteristics and generate IRs")
        self.setStatusBar(status_bar)

    def initialize_exporter(self):
        """Initialize the IR exporter."""
        try:
            self.exporter = IRExporter()
            self.statusBar().showMessage(f"Ready - Loaded {len(self.exporter.ir_reader.irs)} IRs from dataset")
        except Exception as e:
            self.show_message("Error", f"Failed to initialize IR exporter: {str(e)}")

    def on_style_changed(self, style, checked):
        """Handle style selection change."""
        if checked:
            self.selected_style = style
            self.status_label.setText(f"Style: {style} - Ready to generate")

    def generate_irs(self):
        """Generate IRs using the modern interface."""
        if self.worker_thread and self.worker_thread.isRunning():
            return

        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.generate_button.setEnabled(False)
        self.status_label.setText("Generating IRs...")

        # Create worker thread
        self.worker_thread = QThread()
        self.worker = IRGenerationWorker(self.exporter, self.selected_style, 10)
        self.worker.moveToThread(self.worker_thread)

        # Connect signals
        self.worker_thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_generation_finished)
        self.worker.error.connect(self.on_generation_error)
        self.worker.finished.connect(self.worker_thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)

        # Start generation
        self.worker_thread.start()

    def on_generation_finished(self, irs):
        """Handle generation completion."""
        self.last_generated_irs = irs
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)
        self.export_individual_button.setEnabled(True)
        self.export_combined_button.setEnabled(True)
        self.play_button.setEnabled(True)

        self.status_label.setText(f"Generated {len(irs)} IRs successfully")
        self.statusBar().showMessage(f"Generated {len(irs)} IRs - Ready for preview and export")

        # Update IR list display
        self.update_ir_list_display()

        # Select first IR by default
        if irs:
            self.select_ir(0)

    def on_generation_error(self, error_msg):
        """Handle generation error."""
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)
        self.status_label.setText("Generation failed")
        self.show_message("Error", f"IR generation failed: {error_msg}")

    def update_ir_list_display(self):
        """Update the IR list display with enhanced cards in the main panel."""
        # Clear existing widgets
        for i in reversed(range(self.ir_list_layout.count())):
            child = self.ir_list_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        if not self.last_generated_irs:
            # Show placeholder message
            placeholder = QLabel("No IRs generated yet.\nClick 'Generate IRs' to create impulse responses.")
            placeholder.setStyleSheet(f"""
                font-size: 16px;
                color: {MaterialColors.ON_SURFACE_VARIANT};
                text-align: center;
                padding: 40px;
                border: 2px dashed {MaterialColors.SURFACE_VARIANT};
                border-radius: 12px;
                background-color: {MaterialColors.SURFACE_VARIANT};
            """)
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setWordWrap(True)
            self.ir_list_layout.addWidget(placeholder)
            return

        # Update status
        self.ir_status_label.setText(f"{len(self.last_generated_irs)} IRs Generated")
        self.ir_status_label.setStyleSheet(f"""
            font-size: 14px;
            color: {MaterialColors.ON_PRIMARY};
            border: none;
            margin: 0;
            padding: 8px 16px;
            background-color: {MaterialColors.PRIMARY};
            border-radius: 16px;
        """)

        # Create enhanced IR cards
        for i, ir in enumerate(self.last_generated_irs):
            ir_card = self.create_enhanced_ir_item_card(i, ir)
            self.ir_list_layout.addWidget(ir_card)

    def create_enhanced_ir_item_card(self, index, ir):
        """Create an enhanced card for an individual IR item with better visibility."""
        item_card = MaterialCard(elevation=1, interactive=True)
        item_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_CONTAINER};
            border: 2px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 12px;
        """)
        item_card.setMinimumHeight(80)

        layout = QHBoxLayout()
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(20)

        # IR number with prominent styling
        ir_number = QLabel(f"#{index + 1}")
        ir_number.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 700;
            color: {MaterialColors.PRIMARY};
            border: none;
            margin: 0;
            padding: 8px 12px;
            background-color: {MaterialColors.PRIMARY}20;
            border-radius: 8px;
            min-width: 40px;
        """)
        ir_number.setAlignment(Qt.AlignCenter)
        layout.addWidget(ir_number)

        # IR info section
        info_section = QWidget()
        info_layout = QVBoxLayout()
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(4)

        # IR title
        ir_title = QLabel(f"Impulse Response {index + 1}")
        ir_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0;
        """)

        # IR stats with better formatting
        rms = np.sqrt(np.mean(ir**2))
        peak = np.max(np.abs(ir))
        duration = len(ir) / 48000  # Assuming 48kHz sample rate

        stats_text = f"Duration: {duration:.3f}s • RMS: {rms:.4f} • Peak: {peak:.4f}"
        stats_label = QLabel(stats_text)
        stats_label.setStyleSheet(f"""
            font-size: 12px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            border: none;
            margin: 0;
            padding: 0;
        """)

        info_layout.addWidget(ir_title)
        info_layout.addWidget(stats_label)
        info_section.setLayout(info_layout)
        layout.addWidget(info_section, 1)

        # Action buttons
        button_section = QWidget()
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(8)

        # Select button
        select_btn = MaterialButton("Select", "filled")
        select_btn.setMinimumHeight(36)
        select_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MaterialColors.PRIMARY};
                color: {MaterialColors.ON_PRIMARY};
                border: none;
                border-radius: 18px;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.PRIMARY_VARIANT};
            }}
        """)
        select_btn.clicked.connect(lambda: self.select_ir(index))

        # Preview button
        preview_btn = MaterialButton("▶", "outlined")
        preview_btn.setMinimumHeight(36)
        preview_btn.setMaximumWidth(36)
        preview_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {MaterialColors.PRIMARY};
                border: 2px solid {MaterialColors.PRIMARY};
                border-radius: 18px;
                padding: 8px;
                font-weight: 500;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {MaterialColors.HOVER};
            }}
        """)
        preview_btn.clicked.connect(lambda: self.preview_specific_ir(index))

        button_layout.addWidget(select_btn)
        button_layout.addWidget(preview_btn)
        button_section.setLayout(button_layout)
        layout.addWidget(button_section)

        item_card.setLayout(layout)
        return item_card

    def select_ir(self, index):
        """Select an IR for preview and visualization."""
        if not self.last_generated_irs or index >= len(self.last_generated_irs):
            return

        ir = self.last_generated_irs[index]
        self.detail_canvas.update_detail(ir, 48000)
        self.selected_ir_index = index
        self.statusBar().showMessage(f"Selected IR {index + 1} - Ready for preview")

    def preview_selected_ir(self):
        """Preview the selected IR."""
        if not hasattr(self, 'selected_ir_index') or not self.last_generated_irs:
            return

        ir = self.last_generated_irs[self.selected_ir_index]
        self._preview_ir(ir)

    def preview_specific_ir(self, index):
        """Preview a specific IR by index."""
        if not self.last_generated_irs or index >= len(self.last_generated_irs):
            return

        # Select the IR first
        self.select_ir(index)
        # Then preview it
        ir = self.last_generated_irs[index]
        self._preview_ir(ir)

    def _preview_ir(self, ir):
        """Enhanced IR preview with stereo audio support."""
        sample_path = os.path.join(os.path.dirname(__file__), "Sample.wav")
        try:
            # Load sample with stereo detection
            sample_data, sample_rate, channels = stereo_handler.load_audio(sample_path)
            logging.info(f"Loaded sample: {channels} channels, {sample_rate}Hz")
        except Exception as e:
            self.show_message("Error", f"Error loading sample file: {str(e)}")
            return

        try:
            # Perform stereo-aware convolution
            if len(sample_data.shape) > 1:  # Stereo sample
                convolved = stereo_handler.stereo_convolution(sample_data, ir, mode="stereo")
            else:  # Mono sample
                convolved = fftconvolve(sample_data, ir, mode="full")

            # Normalize while preserving stereo balance
            convolved = stereo_handler.normalize_audio(convolved, target_level=0.9)

        except Exception as e:
            self.show_message("Error", f"Error processing audio: {str(e)}")
            return

        # Clean up previous temp file
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
            self.current_temp_file = None

        # Create new temp file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        temp_filename = temp_file.name
        temp_file.close()

        try:
            # Save with stereo format preservation
            stereo_handler.save_audio(convolved, temp_filename, sample_rate)
        except Exception as e:
            self.show_message("Error", f"Error exporting preview file: {str(e)}")
            return

        self.current_temp_file = temp_filename
        self.player.setMedia(QMediaContent(QUrl.fromLocalFile(temp_filename)))
        self.player.play()

    def export_individual_irs(self):
        """Export individual IRs."""
        if not self.last_generated_irs:
            self.show_message("Warning", "No IRs to export. Generate IRs first.")
            return

        folder = QFileDialog.getExistingDirectory(self, "Select Export Folder")
        if not folder:
            return

        try:
            for i, ir in enumerate(self.last_generated_irs):
                filename = self.get_unique_filename(folder, f"IR_{i+1:02d}.wav")
                stereo_handler.save_audio(ir, filename, 48000, 'PCM_24')

            self.show_message("Success", f"Exported {len(self.last_generated_irs)} IRs to {folder}")
        except Exception as e:
            self.show_message("Error", f"Export failed: {str(e)}")

    def export_combined_ir(self):
        """Export combined IR."""
        if not self.last_generated_irs:
            self.show_message("Warning", "No IRs to export. Generate IRs first.")
            return

        filename, _ = QFileDialog.getSaveFileName(self, "Save Combined IR", "Combined_IR.wav", "WAV files (*.wav)")
        if not filename:
            return

        try:
            # Average all IRs
            combined_ir = np.mean(self.last_generated_irs, axis=0)
            stereo_handler.save_audio(combined_ir, filename, 48000, 'PCM_24')
            self.show_message("Success", f"Combined IR exported to {filename}")
        except Exception as e:
            self.show_message("Error", f"Export failed: {str(e)}")

    def get_unique_filename(self, folder, base_filename):
        """Return a unique filename by appending a counter if necessary."""
        filename = os.path.join(folder, base_filename)
        if not os.path.exists(filename):
            return filename
        counter = 1
        name, ext = os.path.splitext(base_filename)
        while os.path.exists(os.path.join(folder, f"{name}_{counter}{ext}")):
            counter += 1
        return os.path.join(folder, f"{name}_{counter}{ext}")

    def show_message(self, title, message):
        """Show a message dialog."""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {MaterialColors.SURFACE_CONTAINER};
                color: {MaterialColors.ON_SURFACE};
            }}
            QMessageBox QPushButton {{
                background-color: {MaterialColors.PRIMARY};
                color: {MaterialColors.ON_PRIMARY};
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 500;
            }}
        """)
        msg_box.exec_()

    def _on_media_status_changed(self, status):
        """Handle media player status changes."""
        if status == QMediaPlayer.EndOfMedia:
            if self.current_temp_file and os.path.exists(self.current_temp_file):
                try:
                    os.remove(self.current_temp_file)
                except Exception:
                    pass
                self.current_temp_file = None

    def closeEvent(self, event):
        """Handle application close."""
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
        event.accept()

if __name__ == "__main__":
    # Enable high-DPI scaling before creating QApplication
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("IR-Alchemist")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Develop Device")

    # Create and show the modern GUI
    window = ModernIRGeneratorGUI()
    window.show()

    sys.exit(app.exec_())
