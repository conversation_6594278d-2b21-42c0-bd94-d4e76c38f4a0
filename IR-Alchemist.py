#!/usr/bin/env python3
"""
IR-Alchemist - Modern Material Design Version
Professional Cabinet IR Generator with Stereo Audio Support
"""

import os
import sys
import tempfile
import soundfile as sf
import numpy as np
from scipy.signal import butter, lfilter, fftconvolve
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QGridLayout, QRadioButton, QButtonGroup, QSizePolicy, QLineEdit, QFileDialog,
    QMessageBox, QPushButton, QLabel, QCheckBox, QSplitter, QGraphicsDropShadowEffect,
    QComboBox, QProgressBar, QStatusBar, QMenuBar, QAction, QToolBar, QScrollArea
)
from PyQt5.QtCore import Qt, QUrl, QObject, QThread, pyqtSignal, pyqtSlot, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QFontDatabase, QFont, QPainter, QColor, QPen, QBrush, QPixmap, QKeySequence
import logging

# Import modern UI framework and stereo audio handler
from modern_ui_framework import *
from stereo_audio_handler import stereo_handler
from native_visualization import NativeDetailCanvas as DetailCanvas

# Using native PyQt5 visualization system
logging.info("Using native PyQt5 visualization system")

# Import the IR reader
from irpkg_reader import IRPKGReader

class IRExporter:
    def __init__(self, package_file=None):
        # Import the IR reader
        from irpkg_reader import IRPKGReader
        
        # Initialize the IR dataset reader with packaged dataset
        self.ir_reader = IRPKGReader(package_file)

    def generate_multiple_irs(self, diversity=1.0, count=10, style="American"):
        """
        Generate multiple IRs by randomly selecting from the dataset without any processing.
        
        Args:
            diversity (float): Ignored - kept for interface compatibility
            count (int): Number of IRs to return (default: 10)
            style (str): Ignored - random selection from entire dataset
            
        Returns:
            list: List of raw, unprocessed IR arrays from the dataset
        """
        # Ensure we have IRs available
        if not self.ir_reader.irs:
            logging.warning("No IRs available in dataset")
            return []
        
        # Calculate how many unique IRs we can provide
        total_irs = len(self.ir_reader.irs)
        unique_count = min(count, total_irs)
        
        # Randomly select unique IRs from the entire dataset (ignore style)
        import random
        selected_indices = random.sample(range(total_irs), unique_count)
        
        # Get the raw IRs without any processing
        raw_irs = [self.ir_reader.irs[i].copy() for i in selected_indices]
        
        # If we need more IRs than unique ones available, fill with additional random selections
        if count > unique_count:
            additional_needed = count - unique_count
            for _ in range(additional_needed):
                # Select random IR (may be duplicate now)
                random_index = random.randint(0, total_irs - 1)
                raw_irs.append(self.ir_reader.irs[random_index].copy())
        
        logging.info(f"Selected {len(raw_irs)} raw IRs from dataset ({unique_count} unique)")
        return raw_irs

class IRGenerationWorker(QObject):
    finished = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, exporter, style, count):
        super().__init__()
        self.exporter = exporter
        self.style = style
        self.count = count

    @pyqtSlot()
    def run(self):
        try:
            irs = self.exporter.generate_multiple_irs(count=self.count, style=self.style)
            self.finished.emit(irs)
        except Exception as e:
            self.error.emit(str(e))

class ModernIRGeneratorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("IR-Alchemist - Professional Audio Processing")
        self.setGeometry(100, 100, 1600, 1000)

        # Apply Material Design theme
        MaterialTheme.apply_global_style(QApplication.instance())

        # Initialize data
        self.last_generated_irs = None
        self.selected_style = "American"
        self.selected_ir_index = 0
        self.player = QMediaPlayer()
        self.worker_thread = None
        self.current_temp_file = None

        # Initialize responsive grid layout
        self.responsive_layout = None

        self.player.mediaStatusChanged.connect(self._on_media_status_changed)

        # Create modern UI
        self.create_modern_interface()

        # Initialize the exporter
        self.initialize_exporter()

    def resizeEvent(self, event):
        """Handle window resize for responsive design."""
        super().resizeEvent(event)
        if self.responsive_layout:
            self.responsive_layout.update_layout_for_breakpoint(self.width())

            # Update responsive styles based on current breakpoint
            current_breakpoint = self.responsive_layout.get_current_breakpoint(self.width())
            MaterialTheme.update_responsive_styles(self, current_breakpoint)
    
    def create_modern_interface(self):
        """Create the modern Material Design interface with responsive layout."""
        # Set main window background
        self.setStyleSheet(f"background-color: {MaterialColors.BACKGROUND};")

        # Create responsive container
        self.responsive_container = ResponsiveContainer()

        # Create header card
        header_card = self.create_header_card()

        # Left panel - Controls
        controls_card = self.create_controls_card()
        controls_card.setMaximumWidth(400)
        controls_card.setMinimumWidth(350)

        # Right panel - Visualization and IR list
        right_panel = self.create_right_panel()

        # Add widgets with responsive configurations
        self.responsive_container.add_responsive_widget(header_card, {
            'xxl': {'row': 0, 'col': 0, 'colspan': 2},
            'xl': {'row': 0, 'col': 0, 'colspan': 2},
            'lg': {'row': 0, 'col': 0, 'colspan': 2},
            'md': {'row': 0, 'col': 0, 'colspan': 2},
            'sm': {'row': 0, 'col': 0, 'colspan': 1},
            'xs': {'row': 0, 'col': 0, 'colspan': 1}
        })

        self.responsive_container.add_responsive_widget(controls_card, {
            'xxl': {'row': 1, 'col': 0, 'colspan': 1},
            'xl': {'row': 1, 'col': 0, 'colspan': 1},
            'lg': {'row': 1, 'col': 0, 'colspan': 1},
            'md': {'row': 1, 'col': 0, 'colspan': 1},
            'sm': {'row': 1, 'col': 0, 'colspan': 1},
            'xs': {'row': 1, 'col': 0, 'colspan': 1}
        })

        self.responsive_container.add_responsive_widget(right_panel, {
            'xxl': {'row': 1, 'col': 1, 'colspan': 1},
            'xl': {'row': 1, 'col': 1, 'colspan': 1},
            'lg': {'row': 1, 'col': 1, 'colspan': 1},
            'md': {'row': 1, 'col': 1, 'colspan': 1},
            'sm': {'row': 2, 'col': 0, 'colspan': 1},  # Stack vertically on small screens
            'xs': {'row': 2, 'col': 0, 'colspan': 1}
        })

        # Set column and row stretch factors
        layout = self.responsive_container.responsive_layout
        layout.setColumnStretch(0, 0)  # Fixed width for controls
        layout.setColumnStretch(1, 1)  # Expandable for visualization
        layout.setRowStretch(0, 0)     # Fixed height for header
        layout.setRowStretch(1, 1)     # Expandable for content
        layout.setRowStretch(2, 1)     # Expandable for stacked content

        # Create status bar
        self.create_status_bar()

        self.setCentralWidget(self.responsive_container)

        # Store reference to layout for resize handling
        self.responsive_layout = self.responsive_container.responsive_layout
    
    def create_header_card(self):
        """Create the modern header card with improved alignment."""
        header_card = MaterialCard(elevation=2)
        header_layout = QVBoxLayout()
        header_layout.setContentsMargins(32, 28, 32, 28)
        header_layout.setSpacing(16)

        # Title section with proper alignment
        title_layout = QHBoxLayout()
        title_layout.setSpacing(16)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        title_label = QLabel("IR-Alchemist")
        title_label.setStyleSheet(f"""
            font-size: 32px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            font-family: 'Segoe UI', 'Roboto', sans-serif;
            border: none;
            margin: 0;
            padding: 0;
            line-height: 1.2;
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        company_label = QLabel("by Develop Device")
        company_label.setStyleSheet(f"""
            font-size: 16px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            font-style: italic;
            border: none;
            margin: 0;
            padding: 4px 0 0 0;
            line-height: 1.2;
        """)
        company_label.setAlignment(Qt.AlignLeft | Qt.AlignBottom)
        company_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        title_layout.addWidget(title_label)
        title_layout.addWidget(company_label)
        title_layout.addStretch()
        header_layout.addLayout(title_layout)

        # Subtitle with clean styling and proper spacing
        subtitle_label = QLabel("Professional Cabinet IR Generator with Stereo Audio Support")
        subtitle_label.setStyleSheet(f"""
            font-size: 18px;
            color: {MaterialColors.PRIMARY};
            font-weight: 500;
            border: none;
            margin: 8px 0 0 0;
            padding: 0;
            line-height: 1.3;
        """)
        subtitle_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        subtitle_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        header_layout.addWidget(subtitle_label)

        # Description with clean styling and proper spacing
        description = QLabel(
            "Generate high-quality impulse responses from our curated dataset. "
            "Select characteristics, generate IRs, and preview with stereo audio processing."
        )
        description.setStyleSheet(f"""
            font-size: 14px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            line-height: 1.5;
            border: none;
            margin: 4px 0 0 0;
            padding: 0;
        """)
        description.setWordWrap(True)
        description.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        description.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        header_layout.addWidget(description)

        header_card.setLayout(header_layout)
        return header_card

    def create_controls_card(self):
        """Create the controls card with improved alignment and clean styling."""
        controls_card = MaterialCard(elevation=1)
        controls_layout = QVBoxLayout()
        controls_layout.setContentsMargins(24, 28, 24, 28)
        controls_layout.setSpacing(24)
        controls_layout.setAlignment(Qt.AlignTop)

        # Card title with clean styling and proper alignment
        title_label = QLabel("IR Generation Controls")
        title_label.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 12px 0;
            line-height: 1.2;
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        title_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        controls_layout.addWidget(title_label)

        # Style selection section
        style_section = self.create_style_selection_section()
        controls_layout.addWidget(style_section)

        # Generation controls
        generation_section = self.create_generation_section()
        controls_layout.addWidget(generation_section)

        # Export controls
        export_section = self.create_export_section()
        controls_layout.addWidget(export_section)

        # Add stretch to push content to top
        controls_layout.addStretch(1)

        controls_card.setLayout(controls_layout)
        controls_card.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)
        return controls_card

    def create_style_selection_section(self):
        """Create the style selection section with clean alignment."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_VARIANT};
            border: none;
        """)

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(20, 18, 20, 18)
        section_layout.setSpacing(12)
        section_layout.setAlignment(Qt.AlignTop)

        # Section title with clean styling and proper alignment
        section_title = QLabel("Sound Characteristic")
        section_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
            line-height: 1.2;
        """)
        section_title.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        section_title.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(section_title)

        # Style radio buttons with improved alignment
        self.style_group = QButtonGroup()
        styles = ["American", "British", "German", "Random"]

        # Create radio button container for better alignment
        radio_container = QWidget()
        radio_layout = QVBoxLayout()
        radio_layout.setContentsMargins(0, 0, 0, 0)
        radio_layout.setSpacing(8)
        radio_layout.setAlignment(Qt.AlignTop)

        for i, style in enumerate(styles):
            radio = MaterialRadioButton(style)
            radio.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

            if style == "American":
                radio.setChecked(True)
                self.selected_style = style

            radio.toggled.connect(lambda checked, s=style: self.on_style_changed(s, checked))
            self.style_group.addButton(radio)
            radio_layout.addWidget(radio)

        radio_container.setLayout(radio_layout)
        section_layout.addWidget(radio_container)

        section_card.setLayout(section_layout)
        section_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        return section_card

    def create_generation_section(self):
        """Create the generation controls section with clean alignment."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_VARIANT};
            border: none;
        """)

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(20, 18, 20, 18)
        section_layout.setSpacing(14)
        section_layout.setAlignment(Qt.AlignTop)

        # Section title with clean styling and proper alignment
        section_title = QLabel("Generation")
        section_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
            line-height: 1.2;
        """)
        section_title.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        section_title.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(section_title)

        # Generate button with proper spacing and alignment
        self.generate_button = MaterialButton("Generate IRs", "filled")
        self.generate_button.setMinimumHeight(48)
        self.generate_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.generate_button.clicked.connect(self.generate_irs)
        section_layout.addWidget(self.generate_button)

        # Progress bar with clean styling and proper alignment
        self.progress_bar = MaterialProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(self.progress_bar)

        # Status label with clean styling and proper alignment
        self.status_label = QLabel("Ready to generate IRs")
        self.status_label.setStyleSheet(f"""
            font-size: 12px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            border: none;
            margin: 0;
            padding: 8px 0 0 0;
            line-height: 1.3;
        """)
        self.status_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        self.status_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.status_label.setWordWrap(True)
        section_layout.addWidget(self.status_label)

        section_card.setLayout(section_layout)
        section_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        return section_card

    def create_export_section(self):
        """Create the export controls section with clean alignment."""
        section_card = MaterialCard(elevation=0)
        section_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_VARIANT};
            border: none;
        """)

        section_layout = QVBoxLayout()
        section_layout.setContentsMargins(20, 18, 20, 18)
        section_layout.setSpacing(12)
        section_layout.setAlignment(Qt.AlignTop)

        # Section title with clean styling and proper alignment
        section_title = QLabel("Export")
        section_title.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
            line-height: 1.2;
        """)
        section_title.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        section_title.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        section_layout.addWidget(section_title)

        # Export buttons with consistent spacing and alignment
        self.export_individual_button = MaterialButton("Export Individual", "outlined")
        self.export_individual_button.setMinimumHeight(44)
        self.export_individual_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.export_individual_button.clicked.connect(self.export_individual_irs)
        self.export_individual_button.setEnabled(False)
        section_layout.addWidget(self.export_individual_button)

        self.export_combined_button = MaterialButton("Export Combined", "outlined")
        self.export_combined_button.setMinimumHeight(44)
        self.export_combined_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.export_combined_button.clicked.connect(self.export_combined_ir)
        self.export_combined_button.setEnabled(False)
        section_layout.addWidget(self.export_combined_button)

        section_card.setLayout(section_layout)
        section_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        return section_card

    def create_right_panel(self):
        """Create the right panel with IR list and visualization."""
        right_widget = QWidget()
        right_layout = QVBoxLayout()
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(24)
        right_layout.setAlignment(Qt.AlignTop)

        # IR List section
        ir_list_card = self.create_ir_list_card()
        ir_list_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        right_layout.addWidget(ir_list_card)

        # Visualization section
        viz_card = self.create_visualization_card()
        viz_card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        right_layout.addWidget(viz_card, 1)

        right_widget.setLayout(right_layout)
        right_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        return right_widget

    def create_ir_list_card(self):
        """Create the IR list card with clean alignment."""
        ir_card = MaterialCard(elevation=1)
        ir_layout = QVBoxLayout()
        ir_layout.setContentsMargins(24, 24, 24, 24)
        ir_layout.setSpacing(18)

        # Title and controls with proper alignment
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(16)

        title_label = QLabel("Generated IRs")
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0;
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # Play button with clean styling
        self.play_button = MaterialButton("▶ Preview", "text")
        self.play_button.setEnabled(False)
        self.play_button.clicked.connect(self.preview_selected_ir)
        title_layout.addWidget(self.play_button)

        ir_layout.addLayout(title_layout)

        # IR list container with clean styling
        self.ir_list_widget = QWidget()
        self.ir_list_widget.setStyleSheet("border: none; background: transparent;")
        self.ir_list_layout = QVBoxLayout()
        self.ir_list_layout.setContentsMargins(0, 0, 0, 0)
        self.ir_list_layout.setSpacing(6)
        self.ir_list_widget.setLayout(self.ir_list_layout)

        # Scroll area with clean borders
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.ir_list_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(200)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: 8px;
                background-color: {MaterialColors.SURFACE};
            }}
            QScrollArea QWidget {{
                background-color: transparent;
                border: none;
            }}
            QScrollBar:vertical {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {MaterialColors.ON_SURFACE_VARIANT};
                border-radius: 4px;
                min-height: 20px;
            }}
        """)
        ir_layout.addWidget(scroll_area)

        ir_card.setLayout(ir_layout)
        return ir_card

    def create_visualization_card(self):
        """Create the visualization card with clean styling."""
        viz_card = MaterialCard(elevation=1)
        viz_layout = QVBoxLayout()
        viz_layout.setContentsMargins(24, 24, 24, 24)
        viz_layout.setSpacing(18)

        # Title with clean styling
        title_label = QLabel("IR Analysis & Visualization")
        title_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0 0 8px 0;
        """)
        title_label.setAlignment(Qt.AlignLeft)
        viz_layout.addWidget(title_label)

        # Detail canvas with clean borders
        self.detail_canvas = DetailCanvas()
        self.detail_canvas.setStyleSheet(f"""
            border: 1px solid {MaterialColors.SURFACE_VARIANT};
            border-radius: 8px;
            background-color: {MaterialColors.SURFACE};
        """)
        viz_layout.addWidget(self.detail_canvas)

        viz_card.setLayout(viz_layout)
        return viz_card

    def create_status_bar(self):
        """Create the modern status bar with clean styling."""
        status_bar = QStatusBar()
        status_bar.setStyleSheet(f"""
            QStatusBar {{
                background-color: {MaterialColors.SURFACE_CONTAINER};
                color: {MaterialColors.ON_SURFACE_VARIANT};
                border-top: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-left: none;
                border-right: none;
                border-bottom: none;
                font-size: 12px;
                padding: 8px 16px;
                margin: 0;
            }}
            QStatusBar::item {{
                border: none;
                margin: 0;
                padding: 0;
            }}
        """)
        status_bar.showMessage("Ready - Select characteristics and generate IRs")
        self.setStatusBar(status_bar)

    def initialize_exporter(self):
        """Initialize the IR exporter."""
        try:
            self.exporter = IRExporter()
            self.statusBar().showMessage(f"Ready - Loaded {len(self.exporter.ir_reader.irs)} IRs from dataset")
        except Exception as e:
            self.show_message("Error", f"Failed to initialize IR exporter: {str(e)}")

    def on_style_changed(self, style, checked):
        """Handle style selection change."""
        if checked:
            self.selected_style = style
            self.status_label.setText(f"Style: {style} - Ready to generate")

    def generate_irs(self):
        """Generate IRs using the modern interface."""
        if self.worker_thread and self.worker_thread.isRunning():
            return

        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.generate_button.setEnabled(False)
        self.status_label.setText("Generating IRs...")

        # Create worker thread
        self.worker_thread = QThread()
        self.worker = IRGenerationWorker(self.exporter, self.selected_style, 10)
        self.worker.moveToThread(self.worker_thread)

        # Connect signals
        self.worker_thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_generation_finished)
        self.worker.error.connect(self.on_generation_error)
        self.worker.finished.connect(self.worker_thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)

        # Start generation
        self.worker_thread.start()

    def on_generation_finished(self, irs):
        """Handle generation completion."""
        self.last_generated_irs = irs
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)
        self.export_individual_button.setEnabled(True)
        self.export_combined_button.setEnabled(True)
        self.play_button.setEnabled(True)

        self.status_label.setText(f"Generated {len(irs)} IRs successfully")
        self.statusBar().showMessage(f"Generated {len(irs)} IRs - Ready for preview and export")

        # Update IR list display
        self.update_ir_list_display()

        # Select first IR by default
        if irs:
            self.select_ir(0)

    def on_generation_error(self, error_msg):
        """Handle generation error."""
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)
        self.status_label.setText("Generation failed")
        self.show_message("Error", f"IR generation failed: {error_msg}")

    def update_ir_list_display(self):
        """Update the IR list display with modern cards."""
        # Clear existing widgets
        for i in reversed(range(self.ir_list_layout.count())):
            child = self.ir_list_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        if not self.last_generated_irs:
            return

        # Create IR cards
        for i, ir in enumerate(self.last_generated_irs):
            ir_card = self.create_ir_item_card(i, ir)
            self.ir_list_layout.addWidget(ir_card)

    def create_ir_item_card(self, index, ir):
        """Create a card for an individual IR item with clean styling."""
        item_card = MaterialCard(elevation=0)
        item_card.setStyleSheet(f"""
            background-color: {MaterialColors.SURFACE_CONTAINER};
            border: none;
            border-radius: 8px;
        """)
        item_card.setMaximumHeight(56)
        item_card.setMinimumHeight(56)

        layout = QHBoxLayout()
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(16)

        # IR info with clean styling
        info_label = QLabel(f"IR {index + 1}")
        info_label.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 500;
            color: {MaterialColors.ON_SURFACE};
            border: none;
            margin: 0;
            padding: 0;
        """)
        info_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        info_label.setMinimumWidth(60)
        layout.addWidget(info_label)

        # IR stats with clean styling
        rms = np.sqrt(np.mean(ir**2))
        peak = np.max(np.abs(ir))
        stats_label = QLabel(f"RMS: {rms:.4f} | Peak: {peak:.4f}")
        stats_label.setStyleSheet(f"""
            font-size: 12px;
            color: {MaterialColors.ON_SURFACE_VARIANT};
            border: none;
            margin: 0;
            padding: 0;
        """)
        stats_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        layout.addWidget(stats_label)

        layout.addStretch()

        # Select button with clean styling
        select_btn = MaterialButton("Select", "text")
        select_btn.clicked.connect(lambda: self.select_ir(index))
        layout.addWidget(select_btn)

        item_card.setLayout(layout)
        return item_card

    def select_ir(self, index):
        """Select an IR for preview and visualization."""
        if not self.last_generated_irs or index >= len(self.last_generated_irs):
            return

        ir = self.last_generated_irs[index]
        self.detail_canvas.update_detail(ir, 48000)
        self.selected_ir_index = index
        self.statusBar().showMessage(f"Selected IR {index + 1} - Ready for preview")

    def preview_selected_ir(self):
        """Preview the selected IR."""
        if not hasattr(self, 'selected_ir_index') or not self.last_generated_irs:
            return

        ir = self.last_generated_irs[self.selected_ir_index]
        self._preview_ir(ir)

    def _preview_ir(self, ir):
        """Enhanced IR preview with stereo audio support."""
        sample_path = os.path.join(os.path.dirname(__file__), "Sample.wav")
        try:
            # Load sample with stereo detection
            sample_data, sample_rate, channels = stereo_handler.load_audio(sample_path)
            logging.info(f"Loaded sample: {channels} channels, {sample_rate}Hz")
        except Exception as e:
            self.show_message("Error", f"Error loading sample file: {str(e)}")
            return

        try:
            # Perform stereo-aware convolution
            if len(sample_data.shape) > 1:  # Stereo sample
                convolved = stereo_handler.stereo_convolution(sample_data, ir, mode="stereo")
            else:  # Mono sample
                convolved = fftconvolve(sample_data, ir, mode="full")

            # Normalize while preserving stereo balance
            convolved = stereo_handler.normalize_audio(convolved, target_level=0.9)

        except Exception as e:
            self.show_message("Error", f"Error processing audio: {str(e)}")
            return

        # Clean up previous temp file
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
            self.current_temp_file = None

        # Create new temp file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        temp_filename = temp_file.name
        temp_file.close()

        try:
            # Save with stereo format preservation
            stereo_handler.save_audio(convolved, temp_filename, sample_rate)
        except Exception as e:
            self.show_message("Error", f"Error exporting preview file: {str(e)}")
            return

        self.current_temp_file = temp_filename
        self.player.setMedia(QMediaContent(QUrl.fromLocalFile(temp_filename)))
        self.player.play()

    def export_individual_irs(self):
        """Export individual IRs."""
        if not self.last_generated_irs:
            self.show_message("Warning", "No IRs to export. Generate IRs first.")
            return

        folder = QFileDialog.getExistingDirectory(self, "Select Export Folder")
        if not folder:
            return

        try:
            for i, ir in enumerate(self.last_generated_irs):
                filename = self.get_unique_filename(folder, f"IR_{i+1:02d}.wav")
                stereo_handler.save_audio(ir, filename, 48000, 'PCM_24')

            self.show_message("Success", f"Exported {len(self.last_generated_irs)} IRs to {folder}")
        except Exception as e:
            self.show_message("Error", f"Export failed: {str(e)}")

    def export_combined_ir(self):
        """Export combined IR."""
        if not self.last_generated_irs:
            self.show_message("Warning", "No IRs to export. Generate IRs first.")
            return

        filename, _ = QFileDialog.getSaveFileName(self, "Save Combined IR", "Combined_IR.wav", "WAV files (*.wav)")
        if not filename:
            return

        try:
            # Average all IRs
            combined_ir = np.mean(self.last_generated_irs, axis=0)
            stereo_handler.save_audio(combined_ir, filename, 48000, 'PCM_24')
            self.show_message("Success", f"Combined IR exported to {filename}")
        except Exception as e:
            self.show_message("Error", f"Export failed: {str(e)}")

    def get_unique_filename(self, folder, base_filename):
        """Return a unique filename by appending a counter if necessary."""
        filename = os.path.join(folder, base_filename)
        if not os.path.exists(filename):
            return filename
        counter = 1
        name, ext = os.path.splitext(base_filename)
        while os.path.exists(os.path.join(folder, f"{name}_{counter}{ext}")):
            counter += 1
        return os.path.join(folder, f"{name}_{counter}{ext}")

    def show_message(self, title, message):
        """Show a message dialog."""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {MaterialColors.SURFACE_CONTAINER};
                color: {MaterialColors.ON_SURFACE};
            }}
            QMessageBox QPushButton {{
                background-color: {MaterialColors.PRIMARY};
                color: {MaterialColors.ON_PRIMARY};
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 500;
            }}
        """)
        msg_box.exec_()

    def _on_media_status_changed(self, status):
        """Handle media player status changes."""
        if status == QMediaPlayer.EndOfMedia:
            if self.current_temp_file and os.path.exists(self.current_temp_file):
                try:
                    os.remove(self.current_temp_file)
                except Exception:
                    pass
                self.current_temp_file = None

    def closeEvent(self, event):
        """Handle application close."""
        if self.current_temp_file and os.path.exists(self.current_temp_file):
            try:
                os.remove(self.current_temp_file)
            except Exception:
                pass
        event.accept()

if __name__ == "__main__":
    # Enable high-DPI scaling before creating QApplication
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("IR-Alchemist")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Develop Device")

    # Create and show the modern GUI
    window = ModernIRGeneratorGUI()
    window.show()

    sys.exit(app.exec_())
