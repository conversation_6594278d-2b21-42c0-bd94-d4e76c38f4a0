#!/usr/bin/env python3
"""
Modern UI Framework for IR-Alchemist
Implements Material Design principles and contemporary UI/UX standards.
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import math

class MaterialColors:
    """Material Design 3.0 color palette with dark theme support."""
    
    # Primary colors
    PRIMARY = "#6750A4"
    PRIMARY_VARIANT = "#7F39FB"
    SECONDARY = "#625B71"
    SECONDARY_VARIANT = "#7D5260"
    
    # Surface colors (Dark theme)
    SURFACE = "#1C1B1F"
    SURFACE_VARIANT = "#49454F"
    SURFACE_CONTAINER = "#211F26"
    SURFACE_CONTAINER_HIGH = "#2B2930"
    SURFACE_CONTAINER_HIGHEST = "#36343B"
    
    # Background
    BACKGROUND = "#141218"
    
    # Text colors
    ON_SURFACE = "#E6E0E9"
    ON_SURFACE_VARIANT = "#CAC4D0"
    ON_PRIMARY = "#FFFFFF"
    ON_SECONDARY = "#FFFFFF"
    
    # Accent colors
    ACCENT_BLUE = "#4285F4"
    ACCENT_GREEN = "#34A853"
    ACCENT_ORANGE = "#FF9800"
    ACCENT_RED = "#EA4335"
    
    # State colors
    HOVER = "#FFFFFF1A"  # 10% white
    FOCUS = "#FFFFFF33"  # 20% white
    PRESSED = "#FFFFFF4D"  # 30% white
    DISABLED = "#FFFFFF61"  # 38% white

class MaterialButton(QPushButton):
    """Material Design button with elevation and ripple effects."""
    
    def __init__(self, text="", button_type="filled", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type  # "filled", "outlined", "text"
        self.elevation = 1
        self.is_hovered = False
        self.is_pressed = False
        
        self.setMinimumHeight(40)
        self.setFont(QFont("Segoe UI", 10, QFont.Medium))
        self.setCursor(Qt.PointingHandCursor)
        
        self._setup_style()
    
    def _setup_style(self):
        """Setup button styling based on type."""
        if self.button_type == "filled":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {MaterialColors.PRIMARY};
                    color: {MaterialColors.ON_PRIMARY};
                    border: none;
                    border-radius: 20px;
                    padding: 10px 24px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {MaterialColors.PRIMARY_VARIANT};
                }}
                QPushButton:pressed {{
                    background-color: {MaterialColors.SECONDARY};
                }}
                QPushButton:disabled {{
                    background-color: {MaterialColors.DISABLED};
                    color: {MaterialColors.ON_SURFACE_VARIANT};
                }}
            """)
        elif self.button_type == "outlined":
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    color: {MaterialColors.PRIMARY};
                    border: 1px solid {MaterialColors.PRIMARY};
                    border-radius: 20px;
                    padding: 10px 24px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {MaterialColors.HOVER};
                }}
                QPushButton:pressed {{
                    background-color: {MaterialColors.PRESSED};
                }}
            """)
        else:  # text button
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    color: {MaterialColors.PRIMARY};
                    border: none;
                    border-radius: 20px;
                    padding: 10px 12px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {MaterialColors.HOVER};
                }}
                QPushButton:pressed {{
                    background-color: {MaterialColors.PRESSED};
                }}
            """)

class MaterialCard(QFrame):
    """Material Design card with elevation and rounded corners."""
    
    def __init__(self, elevation=1, parent=None):
        super().__init__(parent)
        self.elevation = elevation
        
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {MaterialColors.SURFACE_CONTAINER};
                border-radius: 12px;
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
            }}
        """)
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(self.elevation * 4)
        shadow.setOffset(0, self.elevation * 2)
        shadow.setColor(QColor(0, 0, 0, 60))
        self.setGraphicsEffect(shadow)

class MaterialTextField(QLineEdit):
    """Material Design text field with floating label."""
    
    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        self.placeholder_text = placeholder
        
        self.setMinimumHeight(56)
        self.setFont(QFont("Segoe UI", 10))
        
        self.setStyleSheet(f"""
            QLineEdit {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                color: {MaterialColors.ON_SURFACE};
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: 4px;
                padding: 16px 12px;
                font-size: 16px;
            }}
            QLineEdit:focus {{
                border: 2px solid {MaterialColors.PRIMARY};
                background-color: {MaterialColors.SURFACE_CONTAINER};
            }}
            QLineEdit:hover {{
                border: 1px solid {MaterialColors.ON_SURFACE_VARIANT};
            }}
        """)
        
        self.setPlaceholderText(placeholder)

class MaterialProgressBar(QProgressBar):
    """Material Design progress bar with smooth animations."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setMinimumHeight(4)
        self.setMaximumHeight(4)
        self.setTextVisible(False)
        
        self.setStyleSheet(f"""
            QProgressBar {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                border-radius: 2px;
                border: none;
            }}
            QProgressBar::chunk {{
                background-color: {MaterialColors.PRIMARY};
                border-radius: 2px;
            }}
        """)

class MaterialSwitch(QCheckBox):
    """Material Design switch component."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setMinimumSize(52, 32)
        self.setMaximumSize(52, 32)
        
        self.setStyleSheet(f"""
            QCheckBox {{
                spacing: 0px;
            }}
            QCheckBox::indicator {{
                width: 52px;
                height: 32px;
                border-radius: 16px;
                background-color: {MaterialColors.SURFACE_VARIANT};
                border: 2px solid {MaterialColors.SURFACE_VARIANT};
            }}
            QCheckBox::indicator:checked {{
                background-color: {MaterialColors.PRIMARY};
                border: 2px solid {MaterialColors.PRIMARY};
            }}
            QCheckBox::indicator::handle {{
                width: 20px;
                height: 20px;
                border-radius: 10px;
                background-color: {MaterialColors.ON_SURFACE_VARIANT};
                margin: 6px;
            }}
            QCheckBox::indicator:checked::handle {{
                background-color: {MaterialColors.ON_PRIMARY};
                margin-left: 26px;
            }}
        """)

class MaterialTooltip(QToolTip):
    """Enhanced tooltip with Material Design styling."""
    
    @staticmethod
    def setup_global_style():
        """Setup global tooltip styling."""
        QToolTip.setFont(QFont("Segoe UI", 9))
        
        style = f"""
            QToolTip {{
                background-color: {MaterialColors.SURFACE_CONTAINER_HIGHEST};
                color: {MaterialColors.ON_SURFACE};
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 12px;
            }}
        """
        
        return style

class MaterialSnackbar(QWidget):
    """Material Design snackbar for notifications."""
    
    def __init__(self, message, action_text=None, parent=None):
        super().__init__(parent)
        self.message = message
        self.action_text = action_text
        
        self.setFixedHeight(48)
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {MaterialColors.SURFACE_CONTAINER_HIGH};
                border-radius: 4px;
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
            }}
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(16, 0, 8, 0)
        
        # Message label
        message_label = QLabel(message)
        message_label.setFont(QFont("Segoe UI", 10))
        message_label.setStyleSheet(f"color: {MaterialColors.ON_SURFACE};")
        layout.addWidget(message_label)
        
        layout.addStretch()
        
        # Action button (optional)
        if action_text:
            action_btn = MaterialButton(action_text, "text")
            action_btn.setMaximumHeight(36)
            layout.addWidget(action_btn)
        
        self.setLayout(layout)
        
        # Auto-hide timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.hide)
        self.timer.setSingleShot(True)
    
    def show_message(self, duration=4000):
        """Show the snackbar for specified duration."""
        self.show()
        self.timer.start(duration)

class MaterialTheme:
    """Global theme manager for Material Design components."""
    
    @staticmethod
    def apply_global_style(app):
        """Apply global Material Design styling to the application."""
        
        global_style = f"""
            QApplication {{
                background-color: {MaterialColors.BACKGROUND};
                color: {MaterialColors.ON_SURFACE};
                font-family: "Segoe UI", "Roboto", sans-serif;
            }}
            
            QMainWindow {{
                background-color: {MaterialColors.BACKGROUND};
                color: {MaterialColors.ON_SURFACE};
            }}
            
            QWidget {{
                background-color: transparent;
                color: {MaterialColors.ON_SURFACE};
            }}
            
            QLabel {{
                color: {MaterialColors.ON_SURFACE};
                font-size: 14px;
            }}
            
            QGroupBox {{
                font-size: 16px;
                font-weight: 500;
                color: {MaterialColors.ON_SURFACE};
                border: 1px solid {MaterialColors.SURFACE_VARIANT};
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 8px;
                background-color: {MaterialColors.BACKGROUND};
                color: {MaterialColors.PRIMARY};
            }}
            
            QScrollBar:vertical {{
                background-color: {MaterialColors.SURFACE_VARIANT};
                width: 12px;
                border-radius: 6px;
            }}
            
            QScrollBar::handle:vertical {{
                background-color: {MaterialColors.ON_SURFACE_VARIANT};
                border-radius: 6px;
                min-height: 20px;
            }}
            
            QScrollBar::handle:vertical:hover {{
                background-color: {MaterialColors.PRIMARY};
            }}
            
            {MaterialTooltip.setup_global_style()}
        """
        
        app.setStyleSheet(global_style)
