#!/usr/bin/env python3
"""
Comprehensive Test Script for IR-Alchemist GUI
Tests all GUI interactions, alignment, responsiveness, and Material Design components.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QWidget
from PyQt5.QtCore import Qt, QTimer, QRect
from PyQt5.QtTest import QTest
from PyQt5.QtGui import QPixmap

# Import the main application
from IR_Alchemist import Modern<PERSON><PERSON><PERSON>atorG<PERSON>
from modern_ui_framework import MaterialColors, ResponsiveDesign, MaterialButton, MaterialRadioButton, MaterialCard

class GUITester:
    """Comprehensive GUI testing class."""
    
    def __init__(self):
        self.app = None
        self.window = None
        self.test_results = []
        
    def setup_test_environment(self):
        """Setup the test environment."""
        print("Setting up test environment...")
        
        # Enable high-DPI scaling
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("IR-Alchemist-Test")
        
        # Create the main window
        self.window = ModernIRGeneratorGUI()
        self.window.show()
        
        # Wait for window to fully load
        QTest.qWait(1000)
        
        print("✓ Test environment setup complete")
        return True
    
    def test_window_properties(self):
        """Test basic window properties."""
        print("\nTesting window properties...")
        
        try:
            # Test window title
            assert "IR-Alchemist" in self.window.windowTitle()
            self.test_results.append("✓ Window title correct")
            
            # Test window size
            size = self.window.size()
            assert size.width() >= 1600 and size.height() >= 1000
            self.test_results.append("✓ Window size appropriate")
            
            # Test window visibility
            assert self.window.isVisible()
            self.test_results.append("✓ Window is visible")
            
            return True
            
        except Exception as e:
            self.test_results.append(f"✗ Window properties test failed: {e}")
            return False
    
    def test_responsive_layout(self):
        """Test responsive layout system."""
        print("\nTesting responsive layout...")
        
        try:
            # Test different window sizes
            test_sizes = [
                (1920, 1080),  # FullHD
                (2560, 1440),  # QHD
                (3840, 2160),  # 4K
                (1280, 720),   # HD
                (1600, 900)    # WXGA+
            ]
            
            for width, height in test_sizes:
                self.window.resize(width, height)
                QTest.qWait(200)  # Wait for resize to complete
                
                # Check if responsive layout updated
                if hasattr(self.window, 'responsive_layout'):
                    breakpoint = self.window.responsive_layout.get_current_breakpoint(width)
                    self.test_results.append(f"✓ Responsive layout works at {width}x{height} ({breakpoint})")
                
            return True
            
        except Exception as e:
            self.test_results.append(f"✗ Responsive layout test failed: {e}")
            return False
    
    def test_material_design_components(self):
        """Test Material Design components."""
        print("\nTesting Material Design components...")
        
        try:
            # Test buttons
            if hasattr(self.window, 'generate_button'):
                button = self.window.generate_button
                assert isinstance(button, MaterialButton)
                assert button.isEnabled()
                self.test_results.append("✓ Generate button is functional")
            
            # Test radio buttons
            if hasattr(self.window, 'style_group'):
                group = self.window.style_group
                buttons = group.buttons()
                assert len(buttons) > 0
                
                # Test radio button selection
                for button in buttons:
                    if isinstance(button, MaterialRadioButton):
                        # Simulate click
                        QTest.mouseClick(button, Qt.LeftButton)
                        QTest.qWait(100)
                        assert button.isChecked()
                        self.test_results.append(f"✓ Radio button '{button.text()}' works")
                        break
            
            return True
            
        except Exception as e:
            self.test_results.append(f"✗ Material Design components test failed: {e}")
            return False
    
    def test_section_alignment(self):
        """Test section alignment and spacing."""
        print("\nTesting section alignment...")
        
        try:
            # Test header card alignment
            if hasattr(self.window, 'responsive_container'):
                container = self.window.responsive_container
                layout = container.responsive_layout
                
                # Check if widgets are properly aligned
                for i in range(layout.count()):
                    item = layout.itemAt(i)
                    if item and item.widget():
                        widget = item.widget()
                        geometry = widget.geometry()
                        
                        # Check if widget has reasonable position and size
                        assert geometry.x() >= 0
                        assert geometry.y() >= 0
                        assert geometry.width() > 0
                        assert geometry.height() > 0
                
                self.test_results.append("✓ Section alignment is correct")
            
            return True
            
        except Exception as e:
            self.test_results.append(f"✗ Section alignment test failed: {e}")
            return False
    
    def test_high_dpi_support(self):
        """Test high-DPI display support."""
        print("\nTesting high-DPI support...")
        
        try:
            # Test scale factor detection
            scale_factor = ResponsiveDesign.get_scale_factor()
            assert scale_factor > 0
            self.test_results.append(f"✓ Scale factor detected: {scale_factor}")
            
            # Test responsive font sizing
            font_size = ResponsiveDesign.get_responsive_font_size(14)
            assert font_size > 0
            self.test_results.append(f"✓ Responsive font sizing works: {font_size}px")
            
            # Test responsive spacing
            spacing = ResponsiveDesign.get_responsive_spacing(24)
            assert spacing > 0
            self.test_results.append(f"✓ Responsive spacing works: {spacing}px")
            
            return True
            
        except Exception as e:
            self.test_results.append(f"✗ High-DPI support test failed: {e}")
            return False
    
    def test_gui_interactions(self):
        """Test GUI interactions and functionality."""
        print("\nTesting GUI interactions...")
        
        try:
            # Test style selection
            if hasattr(self.window, 'on_style_changed'):
                # This method should exist and be callable
                self.window.on_style_changed("American", True)
                assert self.window.selected_style == "American"
                self.test_results.append("✓ Style selection works")
            
            # Test button states
            if hasattr(self.window, 'export_individual_button'):
                button = self.window.export_individual_button
                # Initially should be disabled
                assert not button.isEnabled()
                self.test_results.append("✓ Export buttons have correct initial state")
            
            return True
            
        except Exception as e:
            self.test_results.append(f"✗ GUI interactions test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests and generate report."""
        print("=" * 60)
        print("IR-ALCHEMIST GUI COMPREHENSIVE TEST SUITE")
        print("=" * 60)
        
        if not self.setup_test_environment():
            print("Failed to setup test environment")
            return False
        
        # Run all tests
        tests = [
            self.test_window_properties,
            self.test_responsive_layout,
            self.test_material_design_components,
            self.test_section_alignment,
            self.test_high_dpi_support,
            self.test_gui_interactions
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed_tests += 1
            except Exception as e:
                self.test_results.append(f"✗ Test {test.__name__} crashed: {e}")
        
        # Generate report
        self.generate_test_report(passed_tests, total_tests)
        
        # Keep window open for manual inspection
        print("\nWindow will remain open for 10 seconds for manual inspection...")
        QTest.qWait(10000)
        
        # Cleanup
        self.window.close()
        self.app.quit()
        
        return passed_tests == total_tests
    
    def generate_test_report(self, passed, total):
        """Generate comprehensive test report."""
        print("\n" + "=" * 60)
        print("TEST RESULTS SUMMARY")
        print("=" * 60)
        
        for result in self.test_results:
            print(result)
        
        print("\n" + "-" * 60)
        print(f"OVERALL RESULT: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! GUI is fully functional.")
        else:
            print(f"⚠️  {total - passed} tests failed. Review issues above.")
        
        print("-" * 60)

if __name__ == "__main__":
    tester = GUITester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
